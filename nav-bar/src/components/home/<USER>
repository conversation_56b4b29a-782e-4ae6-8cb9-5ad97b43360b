<template>
  <div v-if="isDockBarVisible" class="dock-container" :class="{'dock-pure': isPureMode}" @mouseenter="isMouseOverDock = true" @mouseleave="isMouseOverDock = false">
    <div class="dock">
      <!-- 更多应用指示器 -->
      <!-- <Tooltip
        v-if="hasMoreApps"
        title="还有更多应用，请调整屏幕大小查看"
        placement="top"
        :mouseEnterDelay="0.5"
      >
        <div class="dock-item more-indicator">
          <div class="dock-icon">
            <div class="more-icon">
              <span>···</span>
            </div>
          </div>
        </div>
      </Tooltip> -->

      <!-- 应用列表 -->
      <Tooltip
        v-for="app in limitedDockApps"
        :key="`dock-${app.id}`"
        :title="app.name + ':' +app.description"
        placement="top"
        :mouseEnterDelay="0.5"
      >
        <div
          class="dock-item"
          :style="{ '--dock-color': app.color }"
          @click="openApp(app)"
          @contextmenu.prevent="$emit('show-context-menu', $event, app, true)"
        >
          <div class="dock-icon" :style="{ width: iconSize + 'px', height: iconSize + 'px' }">
            <img v-if="(app.icon || app.logo)" :src="concatUrl(app.icon || app.logo)" alt="icon" style="object-fit: contain;" />
            <div v-else class="app-icon-div">
              <span v-text-scale="app.name.slice(0, 6)" class="app-icon-text">{{ app.name.slice(0, 6) }}</span>
            </div>
            <div v-if="app.iscanopen == 2" class="newWindow">
              <svg t="1748943554105" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5226" width="20" height="20"><path d="M914.285714 914.285714h-804.571428v-804.571428h248.685714V0H109.714286C51.2 0 0 51.2 0 109.714286v797.257143c0 65.828571 51.2 117.028571 109.714286 117.028571h797.257143c65.828571 0 109.714286-51.2 109.714285-109.714286V658.285714h-109.714285v256h7.314285zM629.028571 0v109.714286h204.8L277.942857 665.6l80.457143 80.457143 555.885714-555.885714v204.8H1024V0H629.028571z" fill="#999999" p-id="5227"></path></svg>
            </div>
          </div>
        </div>
      </Tooltip>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch, computed, onMounted, onUnmounted } from 'vue'
import { message, Tooltip } from 'ant-design-vue'
import { useUrlStore } from '@/stores/url';
import { useLayoutStore } from '@/stores/layout';
import { GridUtils } from '@/utils/gridLayoutCalculator';

const urlStore = useUrlStore();
const layoutStore = useLayoutStore();

const props = defineProps({
  dockApps: {
    type: Array,
    default: () => []
  },
  isPureMode: {
    type: Boolean,
    default: false
  }
})

console.log(props.dockApps,'dockApps')
const emit = defineEmits(['open-app', 'show-context-menu'])

// 响应式变量
const screenWidth = ref(window.innerWidth)
const maxDisplayCount = ref(12)
const isMouseOverDock = ref(false)

// 动态计算的图标尺寸相关变量
const iconSize = ref(50) // 默认图标尺寸
const gap = ref(10) // 默认间距
const itemWidth = ref(66) // 默认项目宽度（图标+padding+gap）

const isDockBarVisible = computed(() => {
  if (layoutStore.isDockBarFixed) {
    return true;
  }
  return isMouseOverDock.value;
});

// 动态计算图标尺寸和相关参数
const calculateDynamicSizes = async () => {
  try {
    // 使用与Home.vue相同的计算逻辑
    const result = await GridUtils.quickCalculate()

    // 更新响应式变量
    iconSize.value = result.X1 // 图标尺寸
    gap.value = Math.round(result.X2) // 间距尺寸，四舍五入到整数

    // 计算每个dock项目的总宽度（图标 + padding + gap）
    // padding根据图标大小动态调整：图标越大padding越大
    const padding = Math.max(4, Math.min(8, Math.round(iconSize.value * 0.12))) // padding为图标尺寸的12%，范围4-8px
    itemWidth.value = iconSize.value + (padding * 2) + gap.value

    console.log('DockBar动态尺寸计算结果:', {
      图标尺寸: iconSize.value,
      间距: gap.value,
      项目宽度: itemWidth.value,
      padding: padding
    })
  } catch (error) {
    console.warn('DockBar动态尺寸计算失败，使用默认值:', error)
    // 保持默认值不变
  }
}

// 计算每个应用项的宽度（包括间距）- 现在使用动态计算的结果
const calculateItemWidth = () => {
  return itemWidth.value
}

// 计算最大可显示的应用数量
const calculateMaxDisplayCount = () => {
  const currentItemWidth = calculateItemWidth()
  const containerPadding = screenWidth.value <= 480 ? 8 : (screenWidth.value <= 768 ? 12 : 16) // 容器左右padding
  const safeMargin = 40 // 安全边距，确保不会贴边
  const availableWidth = screenWidth.value - containerPadding - safeMargin

  // 计算最大可容纳的应用数量
  const maxCount = Math.floor(availableWidth / currentItemWidth)

  // 确保至少显示1个，最多不超过原始限制
  return Math.max(1, Math.min(maxCount, 12))
}

// 窗口大小变化处理
const handleResize = async () => {
  screenWidth.value = window.innerWidth
  // 先重新计算动态尺寸，再计算最大显示数量
  await calculateDynamicSizes()
  maxDisplayCount.value = calculateMaxDisplayCount()
}

// 防抖处理
let resizeTimer = null
const debouncedHandleResize = () => {
  clearTimeout(resizeTimer)
  resizeTimer = setTimeout(handleResize, 100)
}

// 生命周期钩子
onMounted(() => {
  // 初始化计算
  handleResize()

  // 添加窗口大小变化监听
  window.addEventListener('resize', debouncedHandleResize)

  console.log('DockBar初始化完成:', {
    屏幕宽度: screenWidth.value,
    最大显示数量: maxDisplayCount.value
  })
})

onUnmounted(() => {
  // 清理事件监听器和定时器
  window.removeEventListener('resize', debouncedHandleResize)
  clearTimeout(resizeTimer)
})

// 是否有更多应用无法显示
const hasMoreApps = computed(() => {
  // 基于末尾12个应用来判断屏幕是否能完全显示
  const recentAppsCount = Math.min(props.dockApps.length, 12)
  return recentAppsCount > maxDisplayCount.value
})

// 自适应显示的应用列表
const limitedDockApps = computed(() => {
  const maxCount = maxDisplayCount.value
  const totalApps = props.dockApps.length

  // 先取末尾12个应用（最新收藏的应用）
  const recentApps = totalApps <= 12
    ? [...props.dockApps]
    : props.dockApps.slice(-12)

  // 然后按固定状态排序：isfixed=1的应用排在前面，isfixed=0的应用排在后面
  const sortedApps = recentApps.sort((a, b) => {
    // isfixed=1表示固定，应该排在前面
    // isfixed=0表示未固定，排在后面
    if (a.isfixed === 1 && b.isfixed !== 1) return -1
    if (a.isfixed !== 1 && b.isfixed === 1) return 1
    return 0 // 相同固定状态保持原有顺序
  })

  if (sortedApps.length <= maxCount) {
    // 如果应用数量不超过屏幕最大显示数量，显示全部
    return sortedApps
  } else {
    // 如果超过屏幕最大显示数量，需要为"更多"指示器预留空间
    const availableSlots = hasMoreApps.value ? maxCount - 1 : maxCount
    // 显示前N个应用
    return sortedApps.slice(0, availableSlots)
  }
})

// 监听应用数量变化
watch(props.dockApps, (newVal) => {
  console.log('DockBar应用数量变化:', {
    新数量: newVal.length,
    最大显示: maxDisplayCount.value,
    实际显示: limitedDockApps.value.length
  })

  // 如果应用数量超过当前屏幕能显示的最大数量，给出提示
  if (newVal.length > maxDisplayCount.value) {
    message.warning(`当前屏幕只能显示${maxDisplayCount.value}个应用，已自动调整显示数量`)
  }
})

// 添加一个函数来正确拼接URL
const concatUrl = (path) => {
  return urlStore.concatUrl(path)
}
// 打开应用
function openApp(app) {
  if(app.websiteAddress) {
    app.url = app.websiteAddress
  }
  emit('open-app', app)
}



</script>

<style scoped lang="scss">
/* 底部Dock栏 */
.dock-container {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 18px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 10;
  position: fixed;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  width: max-content;
  max-width: calc(100vw - 40px); /* 确保不超出屏幕宽度，留20px边距 */
  min-width: 66px; /* 至少容纳一个应用 */
}

/* 纯净模式下底部栏样式 */
.dock-pure {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.dock {
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: nowrap;
}

.dock-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  user-select: none;
  // padding: 6px;
  background-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    background-color: rgba(255, 255, 255, 0.3);
  }
}

.dock-icon {
  /* 宽度和高度现在通过动态样式绑定设置 */
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  background-color: var(--dock-color, #ffffff);

  img {
    width: 100%;
    height: 100%;
    object-fit: fill;
    border-radius: 8px;
  }
}

/* 更多应用指示器样式 */
.more-indicator {
  opacity: 0.7;

  &:hover {
    opacity: 1;
  }
}

.more-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: #ffffff;
  font-weight: bold;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dock-container {
    padding: 6px;
    bottom: 10px;
    max-width: calc(100vw - 20px); /* 移动端减少边距 */
    min-width: 54px; /* 中等屏幕最小宽度 */
  }

  .dock {
    gap: 8px;
  }

  .dock-item {
    padding: 5px;
  }

  /* 图标尺寸现在通过动态样式绑定设置，移除固定尺寸 */
}

/* 小屏幕适配 */
@media (max-width: 480px) {
  .dock-container {
    padding: 4px;
    bottom: 8px;
    max-width: calc(100vw - 16px); /* 小屏幕进一步减少边距 */
    min-width: 46px; /* 小屏幕最小宽度 */
  }

  .dock {
    gap: 6px;
  }

  .dock-item {
    padding: 4px;
  }

  /* 图标尺寸现在通过动态样式绑定设置，移除固定尺寸 */
}
.newWindow{
  position: absolute;
  background: rgba($color: #ffffff, $alpha: 0.5);
  padding: 5px;
  right: 0px;
  top: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
